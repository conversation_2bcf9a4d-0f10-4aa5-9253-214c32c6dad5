"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/components/auth/RecaptchaProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/auth/RecaptchaProvider.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecaptchaBadge: function() { return /* binding */ RecaptchaBadge; },\n/* harmony export */   RecaptchaProvider: function() { return /* binding */ RecaptchaProvider; },\n/* harmony export */   useRecaptchaContext: function() { return /* binding */ useRecaptchaContext; },\n/* harmony export */   withRecaptcha: function() { return /* binding */ withRecaptcha; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useRecaptcha__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useRecaptcha */ \"(app-pages-browser)/./src/hooks/useRecaptcha.ts\");\n/* __next_internal_client_entry_do_not_use__ RecaptchaProvider,useRecaptchaContext,withRecaptcha,RecaptchaBadge auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst RecaptchaContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Define pages where reCAPTCHA should be enabled\nconst RECAPTCHA_ENABLED_PAGES = [\n    \"/login\",\n    \"/register\",\n    \"/auth/forgot-password\",\n    \"/login-otp\"\n];\nconst RecaptchaProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const isRecaptchaEnabled = RECAPTCHA_ENABLED_PAGES.includes(pathname);\n    const siteKey = (0,_hooks_useRecaptcha__WEBPACK_IMPORTED_MODULE_3__.getRecaptchaSiteKey)();\n    // Only initialize reCAPTCHA on enabled pages\n    const { isLoaded, isLoading, executeRecaptcha: baseExecuteRecaptcha } = (0,_hooks_useRecaptcha__WEBPACK_IMPORTED_MODULE_3__.useRecaptcha)({\n        siteKey: isRecaptchaEnabled ? siteKey : \"\",\n        action: \"init\"\n    });\n    const executeRecaptcha = async (action)=>{\n        // If reCAPTCHA is not enabled for this page, return null\n        if (!isRecaptchaEnabled) {\n            console.warn(\"reCAPTCHA is not enabled for this page\");\n            return null;\n        }\n        if (!isLoaded || !window.grecaptcha) {\n            console.warn(\"reCAPTCHA not loaded yet\");\n            return null;\n        }\n        try {\n            const token = await window.grecaptcha.execute(siteKey, {\n                action\n            });\n            return token;\n        } catch (error) {\n            console.error(\"reCAPTCHA execution failed:\", error);\n            return null;\n        }\n    };\n    const value = {\n        isLoaded: isRecaptchaEnabled ? isLoaded : false,\n        isLoading: isRecaptchaEnabled ? isLoading : false,\n        executeRecaptcha,\n        isRecaptchaEnabled\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RecaptchaContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RecaptchaProvider, \"6jTnpekqjksJMULcpGuTFxCV21U=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _hooks_useRecaptcha__WEBPACK_IMPORTED_MODULE_3__.useRecaptcha\n    ];\n});\n_c = RecaptchaProvider;\nconst useRecaptchaContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(RecaptchaContext);\n    if (context === undefined) {\n        throw new Error(\"useRecaptchaContext must be used within a RecaptchaProvider\");\n    }\n    return context;\n};\n_s1(useRecaptchaContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Higher-order component to wrap forms with reCAPTCHA\nconst withRecaptcha = (Component)=>{\n    return (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RecaptchaProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n            lineNumber: 88,\n            columnNumber: 5\n        }, undefined);\n};\n// Badge component to show reCAPTCHA branding (required by Google)\nconst RecaptchaBadge = (param)=>{\n    let { className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-xs text-gray-500 mt-4 \".concat(className),\n        children: [\n            \"This site is protected by reCAPTCHA and the Google\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: \"https://policies.google.com/privacy\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"text-blue-600 hover:text-blue-800 underline\",\n                children: \"Privacy Policy\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"and\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: \"https://policies.google.com/terms\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"text-blue-600 hover:text-blue-800 underline\",\n                children: \"Terms of Service\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"apply.\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = RecaptchaBadge;\nvar _c, _c1;\n$RefreshReg$(_c, \"RecaptchaProvider\");\n$RefreshReg$(_c1, \"RecaptchaBadge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/RecaptchaProvider.tsx\n"));

/***/ })

});