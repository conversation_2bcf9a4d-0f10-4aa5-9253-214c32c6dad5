"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"6442d172004b\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NzYzMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjY0NDJkMTcyMDA0YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/auth/RecaptchaProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/auth/RecaptchaProvider.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecaptchaBadge: function() { return /* binding */ RecaptchaBadge; },\n/* harmony export */   RecaptchaProvider: function() { return /* binding */ RecaptchaProvider; },\n/* harmony export */   useRecaptchaContext: function() { return /* binding */ useRecaptchaContext; },\n/* harmony export */   withRecaptcha: function() { return /* binding */ withRecaptcha; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useRecaptcha__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useRecaptcha */ \"(app-pages-browser)/./src/hooks/useRecaptcha.ts\");\n/* __next_internal_client_entry_do_not_use__ RecaptchaProvider,useRecaptchaContext,withRecaptcha,RecaptchaBadge auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\nconst RecaptchaContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Define pages where reCAPTCHA should be enabled\nconst RECAPTCHA_ENABLED_PAGES = [\n    \"/login\",\n    \"/register\",\n    \"/auth/forgot-password\",\n    \"/login-otp\"\n];\nconst RecaptchaProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const isRecaptchaEnabled = RECAPTCHA_ENABLED_PAGES.includes(pathname);\n    const siteKey = (0,_hooks_useRecaptcha__WEBPACK_IMPORTED_MODULE_3__.getRecaptchaSiteKey)();\n    // Only initialize reCAPTCHA on enabled pages\n    const { isLoaded, isLoading, executeRecaptcha: baseExecuteRecaptcha } = (0,_hooks_useRecaptcha__WEBPACK_IMPORTED_MODULE_3__.useRecaptcha)({\n        siteKey: isRecaptchaEnabled ? siteKey : \"\",\n        action: \"init\"\n    });\n    const executeRecaptcha = async (action)=>{\n        // If reCAPTCHA is not enabled for this page, return null\n        if (!isRecaptchaEnabled) {\n            console.warn(\"reCAPTCHA is not enabled for this page\");\n            return null;\n        }\n        if (!isLoaded || !window.grecaptcha) {\n            console.warn(\"reCAPTCHA not loaded yet\");\n            return null;\n        }\n        try {\n            const token = await window.grecaptcha.execute(siteKey, {\n                action\n            });\n            return token;\n        } catch (error) {\n            console.error(\"reCAPTCHA execution failed:\", error);\n            return null;\n        }\n    };\n    const value = {\n        isLoaded: isRecaptchaEnabled ? isLoaded : false,\n        isLoading: isRecaptchaEnabled ? isLoading : false,\n        executeRecaptcha,\n        isRecaptchaEnabled\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RecaptchaContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RecaptchaProvider, \"6jTnpekqjksJMULcpGuTFxCV21U=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _hooks_useRecaptcha__WEBPACK_IMPORTED_MODULE_3__.useRecaptcha\n    ];\n});\n_c = RecaptchaProvider;\nconst useRecaptchaContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(RecaptchaContext);\n    if (context === undefined) {\n        throw new Error(\"useRecaptchaContext must be used within a RecaptchaProvider\");\n    }\n    return context;\n};\n_s1(useRecaptchaContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Higher-order component to wrap forms with reCAPTCHA\nconst withRecaptcha = (Component)=>{\n    return (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RecaptchaProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n            lineNumber: 88,\n            columnNumber: 5\n        }, undefined);\n};\n// Badge component to show reCAPTCHA branding (required by Google)\nconst RecaptchaBadge = (param)=>{\n    let { className = \"\" } = param;\n    _s2();\n    const { isRecaptchaEnabled } = useRecaptchaContext();\n    // Only show the badge if reCAPTCHA is enabled for the current page\n    if (!isRecaptchaEnabled) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-xs text-gray-500 mt-4 \".concat(className),\n        children: [\n            \"This site is protected by reCAPTCHA and the Google\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: \"https://policies.google.com/privacy\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"text-blue-600 hover:text-blue-800 underline\",\n                children: \"Privacy Policy\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"and\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: \"https://policies.google.com/terms\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"text-blue-600 hover:text-blue-800 underline\",\n                children: \"Terms of Service\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"apply.\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(RecaptchaBadge, \"Qu8Y08rsUv7eKnImVX4jb/hJ0bM=\", false, function() {\n    return [\n        useRecaptchaContext\n    ];\n});\n_c1 = RecaptchaBadge;\nvar _c, _c1;\n$RefreshReg$(_c, \"RecaptchaProvider\");\n$RefreshReg$(_c1, \"RecaptchaBadge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/RecaptchaProvider.tsx\n"));

/***/ })

});