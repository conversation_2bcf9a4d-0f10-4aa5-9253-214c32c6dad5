"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/hooks/useRecaptcha.ts":
/*!***********************************!*\
  !*** ./src/hooks/useRecaptcha.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RECAPTCHA_ACTIONS: function() { return /* binding */ RECAPTCHA_ACTIONS; },\n/* harmony export */   getRecaptchaSiteKey: function() { return /* binding */ getRecaptchaSiteKey; },\n/* harmony export */   useRecaptcha: function() { return /* binding */ useRecaptcha; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useRecaptcha,getRecaptchaSiteKey,RECAPTCHA_ACTIONS auto */ \nconst useRecaptcha = (config)=>{\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // If no site key is provided, don't load reCAPTCHA\n        if (!config.siteKey) {\n            setIsLoaded(false);\n            return;\n        }\n        // Check if reCAPTCHA is already loaded\n        if (window.grecaptcha && window.grecaptcha.ready) {\n            setIsLoaded(true);\n            return;\n        }\n        // Load reCAPTCHA script\n        const script = document.createElement(\"script\");\n        script.src = \"https://www.google.com/recaptcha/api.js?render=\".concat(config.siteKey);\n        script.async = true;\n        script.defer = true;\n        script.onload = ()=>{\n            window.grecaptcha.ready(()=>{\n                setIsLoaded(true);\n            });\n        };\n        script.onerror = ()=>{\n            console.error(\"Failed to load reCAPTCHA script\");\n            setIsLoaded(false);\n        };\n        document.head.appendChild(script);\n        return ()=>{\n            // Cleanup script if component unmounts\n            const existingScript = document.querySelector('script[src*=\"recaptcha\"]');\n            if (existingScript) {\n                document.head.removeChild(existingScript);\n            }\n        };\n    }, [\n        config.siteKey\n    ]);\n    const executeRecaptcha = async ()=>{\n        if (!isLoaded || !window.grecaptcha) {\n            console.warn(\"reCAPTCHA not loaded yet\");\n            return null;\n        }\n        setIsLoading(true);\n        try {\n            const token = await window.grecaptcha.execute(config.siteKey, {\n                action: config.action\n            });\n            setIsLoading(false);\n            return token;\n        } catch (error) {\n            console.error(\"reCAPTCHA execution failed:\", error);\n            setIsLoading(false);\n            return null;\n        }\n    };\n    return {\n        isLoaded,\n        isLoading,\n        executeRecaptcha\n    };\n};\n// Utility function to get reCAPTCHA site key from environment\nconst getRecaptchaSiteKey = ()=>{\n    const siteKey = \"6LfjK4grAAAAANb-4AFSjCRQX-1gSAHWPFsTKQ9g\";\n    if (!siteKey) {\n        console.warn(\"NEXT_PUBLIC_RECAPTCHA_SITE_KEY not found in environment variables\");\n        return \"\";\n    }\n    return siteKey;\n};\n// Common reCAPTCHA actions for different forms\nconst RECAPTCHA_ACTIONS = {\n    LOGIN: \"login\",\n    REGISTER: \"register\",\n    FORGOT_PASSWORD: \"forgot_password\",\n    OTP_LOGIN: \"otp_login\",\n    OTP_VERIFY: \"otp_verify\",\n    CONTACT: \"contact\",\n    PAYMENT: \"payment\"\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useRecaptcha.ts\n"));

/***/ })

});