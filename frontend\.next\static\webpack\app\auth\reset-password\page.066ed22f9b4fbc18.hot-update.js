"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/reset-password/page",{

/***/ "(app-pages-browser)/./src/app/auth/reset-password/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/auth/reset-password/page.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ResetPasswordPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_auth_RecaptchaProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/auth/RecaptchaProvider */ \"(app-pages-browser)/./src/components/auth/RecaptchaProvider.tsx\");\n/* harmony import */ var _components_auth_OTPInput__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../components/auth/OTPInput */ \"(app-pages-browser)/./src/components/auth/OTPInput.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ResetPasswordPage() {\n    _s();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"otp\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [otp, setOtp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newPassword, setNewPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [resetToken, setResetToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { executeRecaptcha } = (0,_components_auth_RecaptchaProvider__WEBPACK_IMPORTED_MODULE_5__.useRecaptchaContext)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get email from URL params if coming from forgot password page\n        const emailParam = searchParams.get(\"email\");\n        if (emailParam) {\n            setEmail(emailParam);\n        }\n    }, [\n        searchParams\n    ]);\n    const handleOTPComplete = async (otpValue)=>{\n        if (loading) return; // Prevent multiple submissions\n        setLoading(true);\n        setError(\"\");\n        setOtp(otpValue);\n        try {\n            const apiUrl = \"\".concat(\"http://localhost:5000\" || 0, \"/api/otp/verify-password-reset\");\n            const response = await fetch(apiUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    otp: otpValue\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setResetToken(data.data.resetToken);\n                setStep(\"password\");\n                setError(\"\"); // Clear any previous errors\n            // Don't set success message here, just transition to password step\n            } else {\n                setError(data.message || \"Invalid OTP. Please try again.\");\n            }\n        } catch (err) {\n            console.error(\"OTP verification error:\", err);\n            setError(\"An error occurred. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleOTPSubmit = async (e)=>{\n        e.preventDefault();\n        if (otp.length === 6) {\n            await handleOTPComplete(otp);\n        }\n    };\n    const handlePasswordSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        // Validate passwords match\n        if (newPassword !== confirmPassword) {\n            setError(\"Passwords do not match.\");\n            setLoading(false);\n            return;\n        }\n        // Validate password strength\n        if (newPassword.length < 8) {\n            setError(\"Password must be at least 8 characters long.\");\n            setLoading(false);\n            return;\n        }\n        try {\n            const apiUrl = \"\".concat(\"http://localhost:5000\" || 0, \"/api/auth/reset-password\");\n            const response = await fetch(apiUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    resetToken,\n                    newPassword\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setSuccess(\"Password reset successfully! Redirecting to login...\");\n                setTimeout(()=>{\n                    router.push(\"/login?message=Password reset successfully. Please login with your new password.\");\n                }, 2000);\n            } else {\n                setError(data.message || \"Failed to reset password. Please try again.\");\n            }\n        } catch (err) {\n            console.error(\"Password reset error:\", err);\n            setError(\"An error occurred. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (success && success.includes(\"Password reset successfully\")) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full space-y-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex justify-center items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-white rounded-lg flex items-center justify-center border border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            src: \"/invologo.png\",\n                                            alt: \"InvoNest Logo\",\n                                            width: 32,\n                                            height: 32,\n                                            className: \"object-contain w-full h-full\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-indigo-600\",\n                                    children: \"InvoNest\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-8 h-8 text-green-600\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M5 13l4 4L19 7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                    children: \"Password Reset Successful!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-6\",\n                                    children: success\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex justify-center items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-white rounded-lg flex items-center justify-center border border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            src: \"/invologo.png\",\n                                            alt: \"InvoNest Logo\",\n                                            width: 32,\n                                            height: 32,\n                                            className: \"object-contain w-full h-full\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-indigo-600\",\n                                    children: \"InvoNest\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                            children: step === \"otp\" ? \"Verify OTP\" : \"Set New Password\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: step === \"otp\" ? \"Enter the 6-digit OTP sent to your email address.\" : \"Enter your new password to complete the reset process.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this),\n                step === \"otp\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"email\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Email Address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"email\",\n                                    name: \"email\",\n                                    type: \"email\",\n                                    required: true,\n                                    className: \"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\",\n                                    placeholder: \"Enter your email address\",\n                                    value: email,\n                                    onChange: (e)=>setEmail(e.target.value),\n                                    disabled: loading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"otp\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"OTP Code\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_OTPInput__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    length: 6,\n                                    onComplete: handleOTPComplete,\n                                    loading: loading,\n                                    error: error,\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500 text-center mt-2\",\n                                    children: \"Enter the 6-digit code sent to your email. The form will submit automatically.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, this),\n                        success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/auth/forgot-password\",\n                                className: \"text-sm text-indigo-600 hover:text-indigo-500\",\n                                children: \"Didn't receive OTP? Send again\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_RecaptchaProvider__WEBPACK_IMPORTED_MODULE_5__.RecaptchaBadge, {\n                            className: \"text-center\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handlePasswordSubmit,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"newPassword\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"New Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"newPassword\",\n                                            name: \"newPassword\",\n                                            type: showPassword ? \"text\" : \"password\",\n                                            required: true,\n                                            className: \"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\",\n                                            placeholder: \"Enter new password\",\n                                            value: newPassword,\n                                            onChange: (e)=>setNewPassword(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"h-5 w-5 text-gray-400\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"h-5 w-5 text-gray-400\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"confirmPassword\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Confirm New Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"confirmPassword\",\n                                            name: \"confirmPassword\",\n                                            type: showConfirmPassword ? \"text\" : \"password\",\n                                            required: true,\n                                            className: \"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\",\n                                            placeholder: \"Confirm new password\",\n                                            value: confirmPassword,\n                                            onChange: (e)=>setConfirmPassword(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                            onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                            children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"h-5 w-5 text-gray-400\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"h-5 w-5 text-gray-400\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading || !newPassword || !confirmPassword,\n                                className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    className: \"opacity-25\",\n                                                    cx: \"12\",\n                                                    cy: \"12\",\n                                                    r: \"10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    className: \"opacity-75\",\n                                                    fill: \"currentColor\",\n                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Resetting...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 19\n                                }, this) : \"Reset Password\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_RecaptchaProvider__WEBPACK_IMPORTED_MODULE_5__.RecaptchaBadge, {\n                            className: \"text-center\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/login\",\n                        className: \"text-sm text-indigo-600 hover:text-indigo-500\",\n                        children: \"Back to Sign In\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n            lineNumber: 171,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, this);\n}\n_s(ResetPasswordPage, \"8pswj1pqrGAnMovIKVg+vpvu7pc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _components_auth_RecaptchaProvider__WEBPACK_IMPORTED_MODULE_5__.useRecaptchaContext\n    ];\n});\n_c = ResetPasswordPage;\nvar _c;\n$RefreshReg$(_c, \"ResetPasswordPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/reset-password/page.tsx\n"));

/***/ })

});